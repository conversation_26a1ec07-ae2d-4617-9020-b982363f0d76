# Generated by Django 5.2.4 on 2025-07-11 02:22

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('geo_suggestions', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='location',
            options={'ordering': ['name']},
        ),
        migrations.AddField(
            model_name='location',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='location',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='location',
            name='district',
            field=models.CharField(blank=True, db_index=True, max_length=100),
        ),
        migrations.AddField(
            model_name='location',
            name='full_address',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='location',
            name='house_number',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='location',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='location',
            name='name_ru',
            field=models.CharField(blank=True, help_text='Name in Russian', max_length=255),
        ),
        migrations.AddField(
            model_name='location',
            name='name_uz',
            field=models.CharField(blank=True, help_text='Name in Uzbek', max_length=255),
        ),
        migrations.AddField(
            model_name='location',
            name='postal_code',
            field=models.CharField(blank=True, max_length=10),
        ),
        migrations.AddField(
            model_name='location',
            name='street',
            field=models.CharField(blank=True, db_index=True, max_length=200),
        ),
        migrations.AddField(
            model_name='location',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='location',
            name='name',
            field=models.CharField(db_index=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='location',
            name='type',
            field=models.CharField(choices=[('street', 'Street'), ('district', 'District'), ('landmark', 'Landmark'), ('building', 'Building'), ('park', 'Park'), ('school', 'School'), ('hospital', 'Hospital'), ('mosque', 'Mosque'), ('market', 'Market'), ('government', 'Government Building')], db_index=True, max_length=100),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['name', 'type'], name='geo_locatio_name_a3c8c8_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['district', 'street'], name='geo_locatio_distric_ee3a3e_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['latitude', 'longitude'], name='geo_locatio_latitud_f4d7d6_idx'),
        ),
        migrations.AlterModelTable(
            name='location',
            table='geo_locations',
        ),
    ]

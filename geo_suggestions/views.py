from django.http import JsonResponse
from .models import Location
from django.views.decorators.csrf import csrf_exempt


@csrf_exempt
def suggest_locations(request):
    query = request.GET.get('q', '')
    if not query:
        return JsonResponse({'results': []})
    results = Location.objects.filter(name__icontains=query)[:10]
    data = [
        {
            'name': loc.name,
            'type': loc.type,
            'latitude': loc.latitude,
            'longitude': loc.longitude
        }
        for loc in results
    ]
    return JsonResponse({'results': data})

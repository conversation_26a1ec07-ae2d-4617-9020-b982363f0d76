from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from .models import Location


@api_view(['GET'])
def suggest_locations(request):
    """
    Enhanced location suggestion API for Navoi city

    Parameters:
    - q: search query
    - type: filter by location type
    - district: filter by district
    - limit: number of results (default: 10, max: 50)
    """
    query = request.GET.get('q', '').strip()
    location_type = request.GET.get('type', '')
    district = request.GET.get('district', '')
    limit = min(int(request.GET.get('limit', 10)), 50)

    if not query and not location_type and not district:
        return Response({
            'results': [],
            'count': 0,
            'message': 'Please provide a search query, type, or district filter'
        })

    # Start with active locations
    queryset = Location.objects.filter(is_active=True)

    # Apply search if query provided
    if query:
        queryset = Location.search(query)

    # Apply filters
    if location_type:
        queryset = queryset.filter(type=location_type)

    if district:
        queryset = queryset.filter(district__icontains=district)

    # Limit results
    results = queryset[:limit]

    # Prepare response data
    data = [location.to_dict() for location in results]

    return Response({
        'results': data,
        'count': len(data),
        'query': query,
        'filters': {
            'type': location_type,
            'district': district
        }
    })


@api_view(['GET'])
def get_location_types(request):
    """Get all available location types"""
    types = [{'value': choice[0], 'label': choice[1]} for choice in Location.LOCATION_TYPES]
    return Response({'types': types})


@api_view(['GET'])
def get_districts(request):
    """Get all districts in Navoi city"""
    districts = Location.objects.filter(
        is_active=True,
        district__isnull=False
    ).exclude(district='').values_list('district', flat=True).distinct().order_by('district')

    return Response({'districts': list(districts)})


@api_view(['GET'])
def get_location_detail(request, location_id):
    """Get detailed information about a specific location"""
    try:
        location = Location.objects.get(id=location_id, is_active=True)
        return Response(location.to_dict())
    except Location.DoesNotExist:
        return Response(
            {'error': 'Location not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['GET'])
def search_nearby(request):
    """
    Find locations near given coordinates

    Parameters:
    - lat: latitude
    - lng: longitude
    - radius: search radius in km (default: 1km, max: 10km)
    - type: filter by location type
    """
    try:
        lat = float(request.GET.get('lat'))
        lng = float(request.GET.get('lng'))
    except (TypeError, ValueError):
        return Response(
            {'error': 'Invalid latitude or longitude'},
            status=status.HTTP_400_BAD_REQUEST
        )

    radius = min(float(request.GET.get('radius', 1.0)), 10.0)
    location_type = request.GET.get('type', '')

    # Simple distance calculation (for small areas like Navoi city)
    # Using approximate degree to km conversion for Uzbekistan latitude
    lat_range = radius / 111.0  # 1 degree lat ≈ 111 km
    lng_range = radius / (111.0 * 0.7)  # Adjust for longitude at this latitude

    queryset = Location.objects.filter(
        is_active=True,
        latitude__range=(lat - lat_range, lat + lat_range),
        longitude__range=(lng - lng_range, lng + lng_range)
    )

    if location_type:
        queryset = queryset.filter(type=location_type)

    results = queryset[:20]  # Limit nearby results
    data = [location.to_dict() for location in results]

    return Response({
        'results': data,
        'count': len(data),
        'center': {'lat': lat, 'lng': lng},
        'radius_km': radius
    })


# Legacy endpoint for backward compatibility
@csrf_exempt
@require_http_methods(["GET"])
def suggest_locations_legacy(request):
    """Legacy endpoint - redirects to new API"""
    query = request.GET.get('q', '')
    if not query:
        return JsonResponse({'results': []})

    results = Location.search(query)[:10]
    data = [
        {
            'name': loc.name,
            'type': loc.type,
            'latitude': loc.latitude,
            'longitude': loc.longitude
        }
        for loc in results
    ]
    return JsonResponse({'results': data})

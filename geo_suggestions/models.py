from django.db import models
from django.db.models import Q

# Create your models here.

class Location(models.Model):
    LOCATION_TYPES = [
        ('street', 'Street'),
        ('district', 'District'),
        ('landmark', 'Landmark'),
        ('building', 'Building'),
        ('park', 'Park'),
        ('school', 'School'),
        ('hospital', 'Hospital'),
        ('mosque', 'Mosque'),
        ('market', 'Market'),
        ('government', 'Government Building'),
    ]

    # Basic information
    name = models.CharField(max_length=255, db_index=True)
    name_uz = models.CharField(max_length=255, blank=True, help_text="Name in Uzbek")
    name_ru = models.CharField(max_length=255, blank=True, help_text="Name in Russian")
    type = models.CharField(max_length=100, choices=LOCATION_TYPES, db_index=True)

    # Address details
    full_address = models.TextField(blank=True)
    district = models.CharField(max_length=100, blank=True, db_index=True)
    street = models.Char<PERSON>ield(max_length=200, blank=True, db_index=True)
    house_number = models.CharField(max_length=20, blank=True)
    postal_code = models.CharField(max_length=10, blank=True)

    # Geographic coordinates
    latitude = models.FloatField()
    longitude = models.FloatField()

    # Additional metadata
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'geo_locations'
        indexes = [
            models.Index(fields=['name', 'type']),
            models.Index(fields=['district', 'street']),
            models.Index(fields=['latitude', 'longitude']),
        ]
        ordering = ['name']

    def __str__(self):
        if self.full_address:
            return f"{self.name} - {self.full_address}"
        return f"{self.name} ({self.get_type_display()})"

    @classmethod
    def search(cls, query):
        """Enhanced search functionality"""
        if not query:
            return cls.objects.none()

        # Split query into words for better matching
        words = query.strip().split()
        q_objects = Q()

        for word in words:
            q_objects |= (
                Q(name__icontains=word) |
                Q(name_uz__icontains=word) |
                Q(name_ru__icontains=word) |
                Q(full_address__icontains=word) |
                Q(district__icontains=word) |
                Q(street__icontains=word) |
                Q(description__icontains=word)
            )

        return cls.objects.filter(q_objects, is_active=True).distinct()

    def get_coordinates(self):
        """Return coordinates as a tuple"""
        return (self.latitude, self.longitude)

    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'name': self.name,
            'name_uz': self.name_uz,
            'name_ru': self.name_ru,
            'type': self.type,
            'type_display': self.get_type_display(),
            'full_address': self.full_address,
            'district': self.district,
            'street': self.street,
            'house_number': self.house_number,
            'postal_code': self.postal_code,
            'latitude': self.latitude,
            'longitude': self.longitude,
            'description': self.description,
        }

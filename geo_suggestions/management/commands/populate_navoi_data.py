from django.core.management.base import BaseCommand
from geo_suggestions.models import Location


class Command(BaseCommand):
    help = 'Populate database with Navoi city locations and addresses'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before populating',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing location data...')
            Location.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing data cleared.'))

        self.stdout.write('Populating Navoi city data...')
        
        # Navoi city districts and main areas
        districts_data = [
            # Central districts
            {'name': 'Markaz tumani', 'name_uz': 'Markaz tumani', 'name_ru': 'Центральный район', 
             'type': 'district', 'lat': 40.0844, 'lng': 65.3792},
            {'name': 'Na<PERSON><PERSON><PERSON> shahri', 'name_uz': 'Na<PERSON><PERSON><PERSON> shahri', 'name_ru': 'город Навои', 
             'type': 'district', 'lat': 40.0844, 'lng': 65.3792},
        ]
        
        # Major streets in Navoi
        streets_data = [
            # Main streets
            {'name': 'Alisher Navoiy ko\'chasi', 'name_uz': 'Alisher Navoiy ko\'chasi', 'name_ru': 'улица Алишера Навои',
             'type': 'street', 'district': 'Markaz tumani', 'lat': 40.0850, 'lng': 65.3800},
            {'name': 'Mustaqillik ko\'chasi', 'name_uz': 'Mustaqillik ko\'chasi', 'name_ru': 'улица Независимости',
             'type': 'street', 'district': 'Markaz tumani', 'lat': 40.0840, 'lng': 65.3785},
            {'name': 'Amir Temur ko\'chasi', 'name_uz': 'Amir Temur ko\'chasi', 'name_ru': 'улица Амира Темура',
             'type': 'street', 'district': 'Markaz tumani', 'lat': 40.0860, 'lng': 65.3810},
            {'name': 'Bobur ko\'chasi', 'name_uz': 'Bobur ko\'chasi', 'name_ru': 'улица Бабура',
             'type': 'street', 'district': 'Markaz tumani', 'lat': 40.0830, 'lng': 65.3770},
            {'name': 'Guliston ko\'chasi', 'name_uz': 'Guliston ko\'chasi', 'name_ru': 'улица Гулистан',
             'type': 'street', 'district': 'Markaz tumani', 'lat': 40.0870, 'lng': 65.3820},
        ]
        
        # Important landmarks and buildings
        landmarks_data = [
            # Government buildings
            {'name': 'Navoiy viloyat hokimligi', 'name_uz': 'Navoiy viloyat hokimligi', 'name_ru': 'Хокимият Навоийской области',
             'type': 'government', 'district': 'Markaz tumani', 'street': 'Alisher Navoiy ko\'chasi',
             'lat': 40.0845, 'lng': 65.3795, 'desc': 'Regional government building'},
            
            # Educational institutions
            {'name': 'Navoiy davlat konchilik instituti', 'name_uz': 'Navoiy davlat konchilik instituti', 'name_ru': 'Навоийский государственный горный институт',
             'type': 'school', 'district': 'Markaz tumani', 'street': 'Guliston ko\'chasi',
             'lat': 40.0875, 'lng': 65.3825, 'desc': 'Mining institute'},
            
            # Healthcare
            {'name': 'Navoiy viloyat shifoxonasi', 'name_uz': 'Navoiy viloyat shifoxonasi', 'name_ru': 'Навоийская областная больница',
             'type': 'hospital', 'district': 'Markaz tumani', 'street': 'Mustaqillik ko\'chasi',
             'lat': 40.0835, 'lng': 65.3780, 'desc': 'Regional hospital'},
            
            # Religious sites
            {'name': 'Navoiy Jome masjidi', 'name_uz': 'Navoiy Jome masjidi', 'name_ru': 'Соборная мечеть Навои',
             'type': 'mosque', 'district': 'Markaz tumani', 'street': 'Alisher Navoiy ko\'chasi',
             'lat': 40.0855, 'lng': 65.3805, 'desc': 'Main mosque of Navoi'},
            
            # Parks and recreation
            {'name': 'Alisher Navoiy bog\'i', 'name_uz': 'Alisher Navoiy bog\'i', 'name_ru': 'Парк Алишера Навои',
             'type': 'park', 'district': 'Markaz tumani', 'street': 'Amir Temur ko\'chasi',
             'lat': 40.0865, 'lng': 65.3815, 'desc': 'Central park'},
            
            # Markets
            {'name': 'Markaziy bozor', 'name_uz': 'Markaziy bozor', 'name_ru': 'Центральный рынок',
             'type': 'market', 'district': 'Markaz tumani', 'street': 'Bobur ko\'chasi',
             'lat': 40.0825, 'lng': 65.3765, 'desc': 'Central market'},
        ]
        
        # Create districts
        created_count = 0
        for data in districts_data:
            location, created = Location.objects.get_or_create(
                name=data['name'],
                type=data['type'],
                defaults={
                    'name_uz': data['name_uz'],
                    'name_ru': data['name_ru'],
                    'latitude': data['lat'],
                    'longitude': data['lng'],
                    'district': data.get('district', ''),
                    'full_address': f"{data['name']}, Navoiy, Uzbekistan",
                }
            )
            if created:
                created_count += 1
        
        self.stdout.write(f'Created {created_count} districts')
        
        # Create streets
        created_count = 0
        for data in streets_data:
            location, created = Location.objects.get_or_create(
                name=data['name'],
                type=data['type'],
                defaults={
                    'name_uz': data['name_uz'],
                    'name_ru': data['name_ru'],
                    'latitude': data['lat'],
                    'longitude': data['lng'],
                    'district': data.get('district', ''),
                    'street': data['name'],
                    'full_address': f"{data['name']}, {data.get('district', '')}, Navoiy, Uzbekistan",
                }
            )
            if created:
                created_count += 1
        
        self.stdout.write(f'Created {created_count} streets')
        
        # Create landmarks
        created_count = 0
        for data in landmarks_data:
            location, created = Location.objects.get_or_create(
                name=data['name'],
                type=data['type'],
                defaults={
                    'name_uz': data['name_uz'],
                    'name_ru': data['name_ru'],
                    'latitude': data['lat'],
                    'longitude': data['lng'],
                    'district': data.get('district', ''),
                    'street': data.get('street', ''),
                    'description': data.get('desc', ''),
                    'full_address': f"{data['name']}, {data.get('street', '')}, {data.get('district', '')}, Navoiy, Uzbekistan",
                }
            )
            if created:
                created_count += 1
        
        self.stdout.write(f'Created {created_count} landmarks')

        # Add some residential areas and buildings
        residential_data = [
            {'name': 'Mikrorayon 1', 'name_uz': '1-mikrorayon', 'name_ru': '1-й микрорайон',
             'type': 'district', 'lat': 40.0820, 'lng': 65.3750, 'district': 'Markaz tumani'},
            {'name': 'Mikrorayon 2', 'name_uz': '2-mikrorayon', 'name_ru': '2-й микрорайон',
             'type': 'district', 'lat': 40.0880, 'lng': 65.3850, 'district': 'Markaz tumani'},
            {'name': 'Yangi shahar', 'name_uz': 'Yangi shahar', 'name_ru': 'Новый город',
             'type': 'district', 'lat': 40.0900, 'lng': 65.3900, 'district': 'Markaz tumani'},
        ]

        created_count = 0
        for data in residential_data:
            location, created = Location.objects.get_or_create(
                name=data['name'],
                type=data['type'],
                defaults={
                    'name_uz': data['name_uz'],
                    'name_ru': data['name_ru'],
                    'latitude': data['lat'],
                    'longitude': data['lng'],
                    'district': data.get('district', ''),
                    'full_address': f"{data['name']}, {data.get('district', '')}, Navoiy, Uzbekistan",
                }
            )
            if created:
                created_count += 1

        self.stdout.write(f'Created {created_count} residential areas')

        # Add some specific addresses with house numbers
        addresses_data = [
            {'name': 'Alisher Navoiy ko\'chasi 15', 'street': 'Alisher Navoiy ko\'chasi', 'house': '15',
             'type': 'building', 'lat': 40.0851, 'lng': 65.3801, 'district': 'Markaz tumani'},
            {'name': 'Mustaqillik ko\'chasi 22', 'street': 'Mustaqillik ko\'chasi', 'house': '22',
             'type': 'building', 'lat': 40.0841, 'lng': 65.3786, 'district': 'Markaz tumani'},
            {'name': 'Amir Temur ko\'chasi 8', 'street': 'Amir Temur ko\'chasi', 'house': '8',
             'type': 'building', 'lat': 40.0861, 'lng': 65.3811, 'district': 'Markaz tumani'},
            {'name': 'Bobur ko\'chasi 33', 'street': 'Bobur ko\'chasi', 'house': '33',
             'type': 'building', 'lat': 40.0831, 'lng': 65.3771, 'district': 'Markaz tumani'},
            {'name': 'Guliston ko\'chasi 12', 'street': 'Guliston ko\'chasi', 'house': '12',
             'type': 'building', 'lat': 40.0871, 'lng': 65.3821, 'district': 'Markaz tumani'},
        ]

        created_count = 0
        for data in addresses_data:
            location, created = Location.objects.get_or_create(
                name=data['name'],
                type=data['type'],
                defaults={
                    'name_uz': data['name'],
                    'name_ru': data['name'],
                    'latitude': data['lat'],
                    'longitude': data['lng'],
                    'district': data.get('district', ''),
                    'street': data.get('street', ''),
                    'house_number': data.get('house', ''),
                    'full_address': f"{data.get('street', '')}, {data.get('house', '')}, {data.get('district', '')}, Navoiy, Uzbekistan",
                }
            )
            if created:
                created_count += 1

        self.stdout.write(f'Created {created_count} specific addresses')

        total_locations = Location.objects.count()
        self.stdout.write(
            self.style.SUCCESS(f'Successfully populated database with {total_locations} locations for Navoi city')
        )

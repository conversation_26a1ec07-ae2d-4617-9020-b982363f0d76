import json
import os
from django.core.management.base import BaseCommand
from django.conf import settings
from geo_suggestions.models import Location


class Command(BaseCommand):
    help = 'Import Navoi city data from JSON file'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            default='navoi_city.json',
            help='JSON file path (default: navoi_city.json)',
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before importing',
        )

    def handle(self, *args, **options):
        file_path = options['file']
        
        # If relative path, look in project root
        if not os.path.isabs(file_path):
            file_path = os.path.join(settings.BASE_DIR, file_path)
        
        if not os.path.exists(file_path):
            self.stdout.write(
                self.style.ERROR(f'File not found: {file_path}')
            )
            return

        if options['clear']:
            self.stdout.write('Clearing existing location data...')
            Location.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing data cleared.'))

        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
        except json.JSONDecodeError as e:
            self.stdout.write(
                self.style.ERROR(f'Invalid JSON file: {e}')
            )
            return
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error reading file: {e}')
            )
            return

        if 'locations' not in data:
            self.stdout.write(
                self.style.ERROR('JSON file must contain "locations" array')
            )
            return

        self.stdout.write(f'Importing data from {file_path}...')
        
        created_count = 0
        updated_count = 0
        error_count = 0

        for location_data in data['locations']:
            try:
                # Prepare the data
                name = location_data.get('name', '')
                location_type = location_data.get('type', '')
                
                if not name or not location_type:
                    self.stdout.write(
                        self.style.WARNING(f'Skipping location with missing name or type: {location_data}')
                    )
                    error_count += 1
                    continue

                # Build full address
                address_parts = []
                if location_data.get('street'):
                    address_parts.append(location_data['street'])
                if location_data.get('house_number'):
                    address_parts.append(location_data['house_number'])
                if location_data.get('district'):
                    address_parts.append(location_data['district'])
                address_parts.extend(['Navoiy', 'Uzbekistan'])
                full_address = ', '.join(filter(None, address_parts))

                # Create or update location
                location, created = Location.objects.update_or_create(
                    name=name,
                    type=location_type,
                    defaults={
                        'name_uz': location_data.get('name_uz', name),
                        'name_ru': location_data.get('name_ru', name),
                        'latitude': float(location_data.get('latitude', 0)),
                        'longitude': float(location_data.get('longitude', 0)),
                        'district': location_data.get('district', ''),
                        'street': location_data.get('street', ''),
                        'house_number': location_data.get('house_number', ''),
                        'postal_code': location_data.get('postal_code', ''),
                        'description': location_data.get('description', ''),
                        'full_address': full_address,
                        'is_active': True,
                    }
                )

                if created:
                    created_count += 1
                else:
                    updated_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'Error processing location {location_data.get("name", "unknown")}: {e}')
                )
                error_count += 1
                continue

        # Summary
        total_locations = Location.objects.count()
        self.stdout.write(
            self.style.SUCCESS(
                f'Import completed:\n'
                f'  - Created: {created_count} locations\n'
                f'  - Updated: {updated_count} locations\n'
                f'  - Errors: {error_count} locations\n'
                f'  - Total in database: {total_locations} locations'
            )
        )

        # Show some statistics
        self.stdout.write('\nLocation types in database:')
        for choice in Location.LOCATION_TYPES:
            count = Location.objects.filter(type=choice[0]).count()
            if count > 0:
                self.stdout.write(f'  - {choice[1]}: {count}')

        districts = Location.objects.filter(
            district__isnull=False
        ).exclude(district='').values_list('district', flat=True).distinct()
        
        self.stdout.write(f'\nDistricts: {", ".join(districts)}')

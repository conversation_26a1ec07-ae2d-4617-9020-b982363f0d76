from django.test import TestCase, Client
from django.urls import reverse
from rest_framework import status
from .models import Location



class LocationModelTest(TestCase):
    def setUp(self):
        self.location = Location.objects.create(
            name="Test Street",
            name_uz="Test ko'chasi",
            name_ru="Тестовая улица",
            type="street",
            district="Test District",
            street="Test Street",
            latitude=40.0844,
            longitude=65.3792,
            full_address="Test Street, Test District, Navoiy, Uzbekistan"
        )

    def test_location_creation(self):
        """Test location model creation"""
        self.assertEqual(self.location.name, "Test Street")
        self.assertEqual(self.location.type, "street")
        self.assertTrue(self.location.is_active)

    def test_location_str_method(self):
        """Test location string representation"""
        expected = "Test Street - Test Street, Test District, Navoiy, Uzbekistan"
        self.assertEqual(str(self.location), expected)

    def test_location_search(self):
        """Test location search functionality"""
        results = Location.search("test")
        self.assertEqual(results.count(), 1)
        self.assertEqual(results.first(), self.location)

    def test_location_to_dict(self):
        """Test location to_dict method"""
        data = self.location.to_dict()
        self.assertEqual(data['name'], "Test Street")
        self.assertEqual(data['type'], "street")
        self.assertEqual(data['latitude'], 40.0844)


class LocationAPITest(TestCase):
    def setUp(self):
        self.client = Client()
        # Create test locations
        Location.objects.create(
            name="Alisher Navoiy ko'chasi",
            name_uz="Alisher Navoiy ko'chasi",
            name_ru="улица Алишера Навои",
            type="street",
            district="Markaz tumani",
            street="Alisher Navoiy ko'chasi",
            latitude=40.085,
            longitude=65.38,
            full_address="Alisher Navoiy ko'chasi, Markaz tumani, Navoiy, Uzbekistan"
        )
        Location.objects.create(
            name="Navoiy Jome masjidi",
            name_uz="Navoiy Jome masjidi",
            name_ru="Соборная мечеть Навои",
            type="mosque",
            district="Markaz tumani",
            street="Alisher Navoiy ko'chasi",
            latitude=40.0855,
            longitude=65.3805,
            full_address="Navoiy Jome masjidi, Alisher Navoiy ko'chasi, Markaz tumani, Navoiy, Uzbekistan",
            description="Main mosque of Navoi"
        )

    def test_suggest_locations_api(self):
        """Test location suggestion API"""
        url = reverse('suggest_locations')
        response = self.client.get(url, {'q': 'navoiy'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertIn('results', data)
        self.assertIn('count', data)
        self.assertGreater(data['count'], 0)

    def test_suggest_locations_empty_query(self):
        """Test suggestion API with empty query"""
        url = reverse('suggest_locations')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['count'], 0)

    def test_get_location_types_api(self):
        """Test location types API"""
        url = reverse('location_types')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertIn('types', data)
        self.assertGreater(len(data['types']), 0)

    def test_get_districts_api(self):
        """Test districts API"""
        url = reverse('districts')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertIn('districts', data)

    def test_location_detail_api(self):
        """Test location detail API"""
        location = Location.objects.first()
        url = reverse('location_detail', args=[location.id])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['id'], location.id)
        self.assertEqual(data['name'], location.name)

    def test_location_detail_not_found(self):
        """Test location detail API with non-existent ID"""
        url = reverse('location_detail', args=[9999])
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_search_nearby_api(self):
        """Test nearby search API"""
        url = reverse('search_nearby')
        response = self.client.get(url, {
            'lat': 40.0844,
            'lng': 65.3792,
            'radius': 1.0
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertIn('results', data)
        self.assertIn('center', data)
        self.assertIn('radius_km', data)

    def test_search_nearby_invalid_coordinates(self):
        """Test nearby search API with invalid coordinates"""
        url = reverse('search_nearby')
        response = self.client.get(url, {
            'lat': 'invalid',
            'lng': 65.3792
        })

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_legacy_suggest_api(self):
        """Test legacy suggestion API"""
        url = reverse('suggest_locations_legacy')
        response = self.client.get(url, {'q': 'masjid'})

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('results', data)

    def test_filter_by_type(self):
        """Test filtering by location type"""
        url = reverse('suggest_locations')
        response = self.client.get(url, {'q': 'navoiy', 'type': 'mosque'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        for result in data['results']:
            self.assertEqual(result['type'], 'mosque')

    def test_filter_by_district(self):
        """Test filtering by district"""
        url = reverse('suggest_locations')
        response = self.client.get(url, {'q': 'navoiy', 'district': 'Markaz tumani'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        for result in data['results']:
            self.assertEqual(result['district'], 'Markaz tumani')

from django.contrib import admin
from .models import Location


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ['name', 'type', 'district', 'street', 'is_active', 'created_at']
    list_filter = ['type', 'district', 'is_active', 'created_at']
    search_fields = ['name', 'name_uz', 'name_ru', 'full_address', 'district', 'street']
    list_editable = ['is_active']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'name_uz', 'name_ru', 'type', 'is_active')
        }),
        ('Address Details', {
            'fields': ('full_address', 'district', 'street', 'house_number', 'postal_code')
        }),
        ('Geographic Coordinates', {
            'fields': ('latitude', 'longitude')
        }),
        ('Additional Information', {
            'fields': ('description',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related()

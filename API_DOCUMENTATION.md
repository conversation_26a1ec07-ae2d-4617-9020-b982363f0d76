# Navoi City Geo Suggestion API Documentation

## Overview
This API provides location suggestions and geographic data for Navoi city, Uzbekistan. It supports searching for streets, districts, landmarks, buildings, and other points of interest.

## Base URL
```
http://127.0.0.1:8000/api/v1/
```

## Endpoints

### 1. Location Suggestions
**GET** `/suggest/`

Search for locations based on a query string.

**Parameters:**
- `q` (string): Search query
- `type` (string, optional): Filter by location type
- `district` (string, optional): Filter by district
- `limit` (integer, optional): Number of results (default: 10, max: 50)

**Example:**
```bash
curl "http://127.0.0.1:8000/api/v1/suggest/?q=navoiy&limit=5"
```

**Response:**
```json
{
  "results": [
    {
      "id": 1,
      "name": "Alisher Navoiy ko'chasi",
      "name_uz": "<PERSON>sher Navoiy ko'chasi",
      "name_ru": "улица Алишера Навои",
      "type": "street",
      "type_display": "Street",
      "full_address": "Alisher Navoiy ko'chasi, Markaz tumani, Navoiy, Uzbekistan",
      "district": "Markaz tumani",
      "street": "Alisher Navoiy ko'chasi",
      "house_number": "",
      "postal_code": "",
      "latitude": 40.085,
      "longitude": 65.38,
      "description": ""
    }
  ],
  "count": 1,
  "query": "navoiy",
  "filters": {
    "type": "",
    "district": ""
  }
}
```

### 2. Location Types
**GET** `/types/`

Get all available location types.

**Example:**
```bash
curl "http://127.0.0.1:8000/api/v1/types/"
```

**Response:**
```json
{
  "types": [
    {"value": "street", "label": "Street"},
    {"value": "district", "label": "District"},
    {"value": "landmark", "label": "Landmark"},
    {"value": "building", "label": "Building"},
    {"value": "park", "label": "Park"},
    {"value": "school", "label": "School"},
    {"value": "hospital", "label": "Hospital"},
    {"value": "mosque", "label": "Mosque"},
    {"value": "market", "label": "Market"},
    {"value": "government", "label": "Government Building"}
  ]
}
```

### 3. Districts
**GET** `/districts/`

Get all districts in Navoi city.

**Example:**
```bash
curl "http://127.0.0.1:8000/api/v1/districts/"
```

**Response:**
```json
{
  "districts": ["Markaz tumani"]
}
```

### 4. Location Detail
**GET** `/location/{id}/`

Get detailed information about a specific location.

**Parameters:**
- `id` (integer): Location ID

**Example:**
```bash
curl "http://127.0.0.1:8000/api/v1/location/1/"
```

### 5. Nearby Search
**GET** `/nearby/`

Find locations near given coordinates.

**Parameters:**
- `lat` (float): Latitude
- `lng` (float): Longitude
- `radius` (float, optional): Search radius in km (default: 1km, max: 10km)
- `type` (string, optional): Filter by location type

**Example:**
```bash
curl "http://127.0.0.1:8000/api/v1/nearby/?lat=40.0844&lng=65.3792&radius=1"
```

**Response:**
```json
{
  "results": [...],
  "count": 15,
  "center": {"lat": 40.0844, "lng": 65.3792},
  "radius_km": 1.0
}
```

## Legacy Endpoint

### Location Suggestions (Legacy)
**GET** `/api/suggest/`

Backward compatible endpoint with simplified response format.

**Parameters:**
- `q` (string): Search query

**Example:**
```bash
curl "http://127.0.0.1:8000/api/suggest/?q=bozor"
```

**Response:**
```json
{
  "results": [
    {
      "name": "Markaziy bozor",
      "type": "market",
      "latitude": 40.0825,
      "longitude": 65.3765
    }
  ]
}
```

## Location Types

- `street`: Streets and roads
- `district`: Administrative districts and neighborhoods
- `landmark`: Notable landmarks and monuments
- `building`: Specific buildings and addresses
- `park`: Parks and recreational areas
- `school`: Educational institutions
- `hospital`: Healthcare facilities
- `mosque`: Religious buildings
- `market`: Markets and shopping areas
- `government`: Government buildings

## Error Responses

### 400 Bad Request
```json
{
  "error": "Invalid latitude or longitude"
}
```

### 404 Not Found
```json
{
  "error": "Location not found"
}
```

## CORS Support

The API supports CORS for frontend integration. Allowed origins are configured for development:
- http://localhost:3000
- http://127.0.0.1:3000
- http://localhost:8080
- http://127.0.0.1:8080

## Database Management

### Populate Database
To populate the database with Navoi city data:

```bash
python manage.py populate_navoi_data
```

To clear existing data and repopulate:

```bash
python manage.py populate_navoi_data --clear
```

## Admin Interface

Access the Django admin interface at:
```
http://127.0.0.1:8000/admin/
```

Create a superuser to access the admin:
```bash
python manage.py createsuperuser
```

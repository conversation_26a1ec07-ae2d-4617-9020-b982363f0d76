# Navoi City Geo Suggestion API

A Django REST API for location suggestions and geographic data for Navoi city, Uzbekistan. This API provides comprehensive search functionality for streets, districts, landmarks, buildings, and other points of interest in Navoi.

## Features

- 🔍 **Smart Search**: Fuzzy search across multiple languages (Uzbek, Russian, English)
- 🏢 **Multiple Location Types**: Streets, districts, landmarks, buildings, parks, schools, hospitals, mosques, markets, government buildings
- 📍 **Geospatial Search**: Find locations near specific coordinates
- 🌐 **Multi-language Support**: Names in Uzbek, Russian, and English
- 🔧 **RESTful API**: Clean, well-documented API endpoints
- 📱 **CORS Enabled**: Ready for frontend integration
- 🧪 **Comprehensive Tests**: Full test coverage
- 📊 **Admin Interface**: Django admin for data management

## Quick Start

### Prerequisites
- Python 3.8+
- Django 5.2+
- Virtual environment (recommended)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd geo_suggestion_api
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run migrations**
   ```bash
   python manage.py migrate
   ```

5. **Populate database with Navoi city data**
   ```bash
   python manage.py populate_navoi_data
   ```

6. **Create superuser (optional)**
   ```bash
   python manage.py createsuperuser
   ```

7. **Start development server**
   ```bash
   python manage.py runserver
   ```

The API will be available at `http://127.0.0.1:8000/`

## API Endpoints

### Main Endpoints

- **GET** `/api/v1/suggest/` - Location suggestions
- **GET** `/api/v1/types/` - Available location types
- **GET** `/api/v1/districts/` - Districts in Navoi city
- **GET** `/api/v1/location/{id}/` - Location details
- **GET** `/api/v1/nearby/` - Nearby locations search

### Legacy Endpoint

- **GET** `/api/suggest/` - Backward compatible endpoint

## Usage Examples

### Search for locations
```bash
curl "http://127.0.0.1:8000/api/v1/suggest/?q=navoiy"
```

### Filter by type
```bash
curl "http://127.0.0.1:8000/api/v1/suggest/?q=masjid&type=mosque"
```

### Find nearby locations
```bash
curl "http://127.0.0.1:8000/api/v1/nearby/?lat=40.0844&lng=65.3792&radius=1"
```

### Get location types
```bash
curl "http://127.0.0.1:8000/api/v1/types/"
```

## Database Schema

The main model `Location` includes:

- **Basic Info**: name, name_uz, name_ru, type
- **Address**: full_address, district, street, house_number, postal_code
- **Coordinates**: latitude, longitude
- **Metadata**: description, is_active, created_at, updated_at

## Testing

Run the test suite:
```bash
python manage.py test geo_suggestions
```

## Data Management

### Populate Database
```bash
# Add data to existing database
python manage.py populate_navoi_data

# Clear existing data and repopulate
python manage.py populate_navoi_data --clear
```

### Admin Interface
Access the Django admin at `http://127.0.0.1:8000/admin/` to manage locations.

## Project Structure

```
geo_suggestion_api/
├── backend/                 # Django project settings
├── geo_suggestions/         # Main app
│   ├── models.py           # Location model
│   ├── views.py            # API views
│   ├── admin.py            # Admin configuration
│   ├── tests.py            # Test cases
│   └── management/         # Management commands
│       └── commands/
│           └── populate_navoi_data.py
├── requirements.txt        # Python dependencies
├── API_DOCUMENTATION.md    # Detailed API docs
└── README.md              # This file
```

## Technologies Used

- **Django 5.2.4** - Web framework
- **Django REST Framework** - API framework
- **django-cors-headers** - CORS support
- **SQLite** - Database (development)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).

## Support

For questions or issues, please open an issue on the repository or contact the development team.

---

**Note**: This API is specifically designed for Navoi city, Uzbekistan. The included data covers major streets, districts, landmarks, and points of interest within the city.

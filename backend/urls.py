"""
URL configuration for backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from geo_suggestions.views import (
    suggest_locations, get_location_types, get_districts,
    get_location_detail, search_nearby, suggest_locations_legacy
)

urlpatterns = [
    path('admin/', admin.site.urls),

    # New enhanced API endpoints
    path('api/v1/suggest/', suggest_locations, name='suggest_locations'),
    path('api/v1/types/', get_location_types, name='location_types'),
    path('api/v1/districts/', get_districts, name='districts'),
    path('api/v1/location/<int:location_id>/', get_location_detail, name='location_detail'),
    path('api/v1/nearby/', search_nearby, name='search_nearby'),

    # Legacy endpoint for backward compatibility
    path('api/suggest/', suggest_locations_legacy, name='suggest_locations_legacy'),
]

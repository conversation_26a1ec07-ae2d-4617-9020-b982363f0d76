# Navoi City Geo API - Postman & cURL Examples

## Server Information
- **Base URL**: `http://127.0.0.1:8000`
- **API Version**: v1
- **Content-Type**: `application/json`

## 1. Location Suggestions

### Basic Search
```bash
# Search for "navoiy"
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=navoiy" \
  -H "Accept: application/json"

# Search for "masjid" (mosque)
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=masjid" \
  -H "Accept: application/json"

# Search for "maktab" (school)
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=maktab" \
  -H "Accept: application/json"
```

### Advanced Search with Filters
```bash
# Search with type filter
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=navoiy&type=street" \
  -H "Accept: application/json"

# Search with district filter
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=&district=Markaz%20tumani" \
  -H "Accept: application/json"

# Search with limit
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=ko%27chasi&limit=5" \
  -H "Accept: application/json"

# Combined filters
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=navoiy&type=mosque&district=Markaz%20tumani&limit=3" \
  -H "Accept: application/json"
```

## 2. Location Types

### Get All Available Types
```bash
curl -X GET "http://127.0.0.1:8000/api/v1/types/" \
  -H "Accept: application/json"
```

## 3. Districts

### Get All Districts
```bash
curl -X GET "http://127.0.0.1:8000/api/v1/districts/" \
  -H "Accept: application/json"
```

## 4. Location Details

### Get Specific Location by ID
```bash
# Get location with ID 1
curl -X GET "http://127.0.0.1:8000/api/v1/location/1/" \
  -H "Accept: application/json"

# Get location with ID 5
curl -X GET "http://127.0.0.1:8000/api/v1/location/5/" \
  -H "Accept: application/json"

# Non-existent location (will return 404)
curl -X GET "http://127.0.0.1:8000/api/v1/location/9999/" \
  -H "Accept: application/json"
```

## 5. Nearby Search

### Find Locations Near Coordinates
```bash
# Search near city center (1km radius)
curl -X GET "http://127.0.0.1:8000/api/v1/nearby/?lat=40.0844&lng=65.3792&radius=1" \
  -H "Accept: application/json"

# Search near city center (500m radius)
curl -X GET "http://127.0.0.1:8000/api/v1/nearby/?lat=40.0844&lng=65.3792&radius=0.5" \
  -H "Accept: application/json"

# Search with type filter
curl -X GET "http://127.0.0.1:8000/api/v1/nearby/?lat=40.0844&lng=65.3792&radius=2&type=school" \
  -H "Accept: application/json"

# Search for mosques nearby
curl -X GET "http://127.0.0.1:8000/api/v1/nearby/?lat=40.0844&lng=65.3792&radius=1.5&type=mosque" \
  -H "Accept: application/json"
```

## 6. Legacy Endpoint

### Backward Compatible Search
```bash
# Legacy format
curl -X GET "http://127.0.0.1:8000/api/suggest/?q=bozor" \
  -H "Accept: application/json"

curl -X GET "http://127.0.0.1:8000/api/suggest/?q=shifoxona" \
  -H "Accept: application/json"
```

## 7. Specific Navoi City Examples

### Search for Streets
```bash
# Find all streets with "ko'chasi"
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=ko%27chasi&type=street" \
  -H "Accept: application/json"

# Search for Alisher Navoi street
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=Alisher%20Navoiy" \
  -H "Accept: application/json"
```

### Search for Educational Institutions
```bash
# Find schools
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?type=school" \
  -H "Accept: application/json"

# Find mining institute
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=konchilik" \
  -H "Accept: application/json"
```

### Search for Healthcare
```bash
# Find hospitals
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?type=hospital" \
  -H "Accept: application/json"

# Search for polyclinic
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=poliklinika" \
  -H "Accept: application/json"
```

### Search for Religious Sites
```bash
# Find all mosques
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?type=mosque" \
  -H "Accept: application/json"

# Search for Friday mosque
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=Jome" \
  -H "Accept: application/json"
```

### Search for Markets
```bash
# Find all markets
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?type=market" \
  -H "Accept: application/json"

# Search for central market
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=Markaziy%20bozor" \
  -H "Accept: application/json"
```

### Search for Microdistricts
```bash
# Find all microdistricts
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=mikrorayon" \
  -H "Accept: application/json"

# Search for specific microdistrict
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=1-mikrorayon" \
  -H "Accept: application/json"
```

## 8. Error Handling Examples

### Invalid Coordinates
```bash
# Invalid latitude
curl -X GET "http://127.0.0.1:8000/api/v1/nearby/?lat=invalid&lng=65.3792" \
  -H "Accept: application/json"

# Missing coordinates
curl -X GET "http://127.0.0.1:8000/api/v1/nearby/" \
  -H "Accept: application/json"
```

## 9. Postman Collection JSON

Save this as `navoi_api.postman_collection.json`:

```json
{
  "info": {
    "name": "Navoi City Geo API",
    "description": "API for Navoi city location suggestions and geographic data",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "base_url",
      "value": "http://127.0.0.1:8000",
      "type": "string"
    }
  ],
  "item": [
    {
      "name": "Location Suggestions",
      "item": [
        {
          "name": "Basic Search",
          "request": {
            "method": "GET",
            "header": [],
            "url": {
              "raw": "{{base_url}}/api/v1/suggest/?q=navoiy",
              "host": ["{{base_url}}"],
              "path": ["api", "v1", "suggest"],
              "query": [{"key": "q", "value": "navoiy"}]
            }
          }
        },
        {
          "name": "Search with Type Filter",
          "request": {
            "method": "GET",
            "header": [],
            "url": {
              "raw": "{{base_url}}/api/v1/suggest/?q=navoiy&type=street",
              "host": ["{{base_url}}"],
              "path": ["api", "v1", "suggest"],
              "query": [
                {"key": "q", "value": "navoiy"},
                {"key": "type", "value": "street"}
              ]
            }
          }
        }
      ]
    },
    {
      "name": "Location Types",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{base_url}}/api/v1/types/",
          "host": ["{{base_url}}"],
          "path": ["api", "v1", "types"]
        }
      }
    },
    {
      "name": "Districts",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{base_url}}/api/v1/districts/",
          "host": ["{{base_url}}"],
          "path": ["api", "v1", "districts"]
        }
      }
    },
    {
      "name": "Nearby Search",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{base_url}}/api/v1/nearby/?lat=40.0844&lng=65.3792&radius=1",
          "host": ["{{base_url}}"],
          "path": ["api", "v1", "nearby"],
          "query": [
            {"key": "lat", "value": "40.0844"},
            {"key": "lng", "value": "65.3792"},
            {"key": "radius", "value": "1"}
          ]
        }
      }
    }
  ]
}
```

## 10. Testing Commands

### Quick API Health Check
```bash
# Test if API is running
curl -X GET "http://127.0.0.1:8000/api/v1/types/" \
  -w "HTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  -s -o /dev/null

# Test search functionality
curl -X GET "http://127.0.0.1:8000/api/v1/suggest/?q=test" \
  -w "HTTP Status: %{http_code}\n" \
  -s -o /dev/null
```

# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2016,2020-2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-02-01 22:06+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Lower Sorbian (http://www.transifex.com/django/django/"
"language/dsb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: dsb\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n"
"%100==4 ? 2 : 3);\n"

msgid "Administrative Documentation"
msgstr "Administratiwna dokumentacija"

msgid "Home"
msgstr "Startowy bok"

msgid "Documentation"
msgstr "Dokumentacija"

msgid "Bookmarklets"
msgstr "Skriptowe cytańske znamjenja"

msgid "Documentation bookmarklets"
msgstr "Skriptowe cytańske znamjenja dokumentacije"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Aby skriptowe cytańske znamjenja instalěrował, śěgniśo wótkaz do swójeje "
"rědki cytańskich znamjenjow abo klikniśo z pšaweju tastu myški a pśidajśo "
"jen swójim cytańskim znamjenjam. Něnto móžośo skriptowe cytańske znamje z "
"drugego boka na sedle wubraś."

msgid "Documentation for this page"
msgstr "Dokumentacija za toś ten bok"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Zmóžnja wam, z někakego boka do dokumentacije naglěda skócyś, kótaryž twóri "
"ten bok."

msgid "Tags"
msgstr "Wobznamjenja"

msgid "List of all the template tags and their functions."
msgstr "Lisćina wšych pśedłogowych wobznamjenjow a jich funkcijow."

msgid "Filters"
msgstr "Filtry"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtry su akcije, kótarež daju se na wariable w pśedłoze nałožyś, aby wudaśe "
"změnili."

msgid "Models"
msgstr "Modele"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modele su wopisanja wšych objektow w systemje a jich pśisłušnych pólow. "
"Kuždy model ma lisćinu pólow, na kótarež maju pśistup ako pśedłogowe "
"wariable."

msgid "Views"
msgstr "Naglědy"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Kuždy bok na zjawnem sedle twóri se pśez naglěd. Naglěd definěrujo, kótara "
"pśedłoga se wužywa, aby se bok napórał a kótare objekty su tej pśedłoze k "
"dispoziciji."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Rědy za waš wobglědowak za malsny pśistup na adminstratorowu funkcionalnosć."

msgid "Please install docutils"
msgstr "Pšosym instalěrujśo docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Administratorowy dokumentaciski system pomina se Pythonowu biblioteku <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Pšosym pšosćo swóje administratory, <a href=\"%(link)s\">docutils</a> "
"instalěrowaś."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Póla"

msgid "Field"
msgstr "Pólo"

msgid "Type"
msgstr "Typ"

msgid "Description"
msgstr "Wopisanje"

msgid "Methods with arguments"
msgstr "Metody z argumentami"

msgid "Method"
msgstr "Metoda"

msgid "Arguments"
msgstr "Argumenty"

msgid "Back to Model documentation"
msgstr "Slědk k modelowej dokumentaciji"

msgid "Model documentation"
msgstr "Modelowa dokumentacija"

msgid "Model groups"
msgstr "Modelowe kupki"

msgid "Templates"
msgstr "Pśedłogi"

#, python-format
msgid "Template: %(name)s"
msgstr "Pśedłoga: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Pśedłoga: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Pytańska sćažka za pśedłogu <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(njeeksistěrujo)"

msgid "Back to Documentation"
msgstr "Slědk k dokumentaciji"

msgid "Template filters"
msgstr "Pśedłogowe filtry"

msgid "Template filter documentation"
msgstr "Dokumentacija za pśedłogowe filtry"

msgid "Built-in filters"
msgstr "Zatwarjone filtry"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Aby toś te filtry wužywał, zasajźćo <code>%(code)s</code> do wójeje "
"pśedłogi, nježli až wužywaśo filter."

msgid "Template tags"
msgstr "Pśedłogowe wobznamjenja"

msgid "Template tag documentation"
msgstr "Dokumentacija za pśedłogowe wobznamjenja"

msgid "Built-in tags"
msgstr "Zatworjone wobznamjenja"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Aby toś te wobznamjenja wužywał, zasajźćo <code>%(code)s</code> do wójeje "
"pśedłogi, nježli až wužywaśo wobznamjenje."

#, python-format
msgid "View: %(name)s"
msgstr "Naglěd: %(name)s"

msgid "Context:"
msgstr "Kontekst:"

msgid "Templates:"
msgstr "Pśedłogi"

msgid "Back to View documentation"
msgstr "Slědk k naglědowej dokumentaciji"

msgid "View documentation"
msgstr "Naglědowa dokumentacija"

msgid "Jump to namespace"
msgstr "K mjenjowemu rumoju skócyś"

msgid "Empty namespace"
msgstr "Prozny mjenjowy rum"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Naglědy pó mjenjowem rumje %(name)s"

msgid "Views by empty namespace"
msgstr "Naglědy pó proznem mjenjowem rumje"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Naglědowa funkcija: <code>%(full_name)s</code>. Mě: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "wobznamjenje:"

msgid "filter:"
msgstr "filter:"

msgid "view:"
msgstr "naglěd:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Nałoženje %(app_label)r njenamakane"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Model %(model_name)r njejo se w nałoženju %(app_label)r namakał"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "pśisłušny objekt %(app_label)s.%(data_type)s“"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "pśisłušne objekty „%(app_label)s.%(object_name)s"

#, python-format
msgid "all %s"
msgstr "wšykne %s"

#, python-format
msgid "number of %s"
msgstr "ůicba %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "zda se, až %s njejo objekt urlpattern"

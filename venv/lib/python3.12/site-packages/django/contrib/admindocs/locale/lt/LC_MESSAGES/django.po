# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# Ko<PERSON><PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2011
# <AUTHOR> <EMAIL>, 2015-2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Lithuanian (http://www.transifex.com/django/django/language/"
"lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < "
"11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? "
"1 : n % 1 != 0 ? 2: 3);\n"

msgid "Administrative Documentation"
msgstr "Administravimo dokumentacija"

msgid "Home"
msgstr "Pradinis"

msgid "Documentation"
msgstr "Dokumentacija"

msgid "Bookmarklets"
msgstr "Greitosios žymės"

msgid "Documentation bookmarklets"
msgstr "Doumentacijos greitosios žymės"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Greitųjų žymių įdiegimui, nutempkite nuorodą į greitųjų žymių juostą, arba "
"spauskite dešinį pelės klavišą ir pridėkite prie greitųjų žymių. Dabar "
"galite pasirinkti greitąją žymę iš bet kurio tinklalapio puslapio."

msgid "Documentation for this page"
msgstr "Šio puslapio dokumentacija"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Pereina iš bet kurio puslapio į jo view funkcijos dokumentaciją, kuri "
"sukūria tą puslapį"

msgid "Tags"
msgstr "Žymenos"

msgid "List of all the template tags and their functions."
msgstr "Sąrašas visų šablono žymenų ir jų funkcijų."

msgid "Filters"
msgstr "Filtrai"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtrai yra veiksmai, kurie gali būti pritaikyti šablono kintamiesiems, kad "
"pakeisti jų išvestį."

msgid "Models"
msgstr "Modeliai"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modeliai yra visų sistemos objektų ir su jais susijusių laukų apibūdinimai. "
"Kiekvienas modelis turi sąrašą laukų, kuriuos galima pasiekti kaip šablono "
"kintamuosius."

msgid "Views"
msgstr "Rodiniai"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Kiekvienas puslapis viešame tinklalapyje sugeneruojamas pagal rodmenis. "
"Rodmuo apibūdina kuris šablonas yra naudojamas generuojant puslapį ir kurie "
"objektai yra suteikiami tam šablonui."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Įrankiai Jūsų naršyklėi, kad greitai pasiektumėte administravimo "
"funkcionalumą."

msgid "Please install docutils"
msgstr "Įdiekite docutils"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Administravimo dokumentacijos sistema reikalauja Python'o bibliotekos <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Prašykite kad administratorius įdiegtų <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Modelis: %(name)s"

msgid "Fields"
msgstr "Laukai"

msgid "Field"
msgstr "Laukas"

msgid "Type"
msgstr "Tipas"

msgid "Description"
msgstr "Aprašymas"

msgid "Methods with arguments"
msgstr "Metodai su argumentais"

msgid "Method"
msgstr "Metodas"

msgid "Arguments"
msgstr "Argumentai"

msgid "Back to Model documentation"
msgstr "Grįžti į modelio dokumentaciją"

msgid "Model documentation"
msgstr "Modelio dokumentacija"

msgid "Model groups"
msgstr "Modelio grupės"

msgid "Templates"
msgstr "Šablonai"

#, python-format
msgid "Template: %(name)s"
msgstr "Šablonas: %(name)s"

#, python-format
msgid "Template: \"%(name)s\""
msgstr "Šablonas: \"%(name)s\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr "Ieškoti kelio šablonui \"%(name)s\":"

msgid "(does not exist)"
msgstr "(neegzistuoja)"

msgid "Back to Documentation"
msgstr "Grįžti į dokumentacija"

msgid "Template filters"
msgstr "Šablono filtrai"

msgid "Template filter documentation"
msgstr "Šablono filtro dokumentacija"

msgid "Built-in filters"
msgstr "Standartiniai filtrai"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Kad panaudoti šiuos filtrus, prieš naudojant filtrą šablone įveskite <code>"
"%(code)s</code>."

msgid "Template tags"
msgstr "Šablono žymenos"

msgid "Template tag documentation"
msgstr "Šablono žymenos dokumentacija"

msgid "Built-in tags"
msgstr "Standartinės žymenos"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Kad panaudoti šias žymenas, prieš naudojant žymeną šablone įveskite <code>"
"%(code)s</code>."

#, python-format
msgid "View: %(name)s"
msgstr "Rodinys: %(name)s"

msgid "Context:"
msgstr "Konstekstas:"

msgid "Templates:"
msgstr "Šablonai:"

msgid "Back to View documentation"
msgstr "Grįžti į rodinio dokumentaciją"

msgid "View documentation"
msgstr "Žiūrėti dokumentaciją"

msgid "Jump to namespace"
msgstr "Eiti į vardų plotmę"

msgid "Empty namespace"
msgstr "Tuščia vardų plotmė"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Rodiniai pagal vardų plotmes %(name)s"

msgid "Views by empty namespace"
msgstr "Rodiniai pagal tuščias vardų plotmes."

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Rodinio funkcija: <code>%(full_name)s</code>. Pavadinimas: <code>"
"%(url_name)s</code>.\n"

msgid "tag:"
msgstr "žymė:"

msgid "filter:"
msgstr "filtras:"

msgid "view:"
msgstr "vaizdas:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "%(app_label)r aplikacija nerasta"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Modelis %(model_name)r programoje %(app_label)r nerastas"

msgid "model:"
msgstr "modelis:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "susijęs `%(app_label)s.%(data_type)s` objektas"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "susiję`%(app_label)s.%(object_name)s` objektai"

#, python-format
msgid "all %s"
msgstr "visi %s"

#, python-format
msgid "number of %s"
msgstr "%s skaičius"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s neatrodo kaip urlpattern objektas"

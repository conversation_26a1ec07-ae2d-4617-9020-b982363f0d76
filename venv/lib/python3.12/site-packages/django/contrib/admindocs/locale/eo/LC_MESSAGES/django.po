# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2013-2016
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-04-13 08:19+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Esperanto (http://www.transifex.com/django/django/language/"
"eo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: eo\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Administra dokumentaro"

msgid "Home"
msgstr "Ĉefpaĝo"

msgid "Documentation"
msgstr "Dokumentaro"

msgid "Bookmarklets"
msgstr "Legosignetoj"

msgid "Documentation bookmarklets"
msgstr "Dokumentaraj legosignetoj"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Por instali legosignetoj, trenu la ligilon ĝis via legosignan ilobreton, aŭ "
"dekstre klaku la ligilon kaj aldoni ĝin al viaj markiloj. Nun vi povas "
"elekti la legosignetoj de ajna paĝo en la retejo."

msgid "Documentation for this page"
msgstr "Dokumentaro por tiu paĝo"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Sendas vin el ajna paĝo, al la dokumentado de la vido kiu generis tiun paĝon."

msgid "Tags"
msgstr "Etikedoj"

msgid "List of all the template tags and their functions."
msgstr "Listigi ĉiujn ŝablonmarkojn kaj iliajn funkciojn."

msgid "Filters"
msgstr "Filtriloj"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtriloj estas agoj kiuj povas esti aplikitaj al variabloj en ŝablono por "
"ŝanĝi la eligon."

msgid "Models"
msgstr "Modeloj"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modeloj estas priskriboj de ĉiuj objektoj en la sistemo kaj iliaj rilataj "
"kampoj. Ĉiu modelo havas liston de kampoj kiu povas esti alirita kiel "
"ŝablona variabloj"

msgid "Views"
msgstr "Vidoj"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Ĉiu paĝo sur la publika retejo estas produktita per vido. La vido difinas "
"kiun ŝablonon estas uzita por produkti la paĝon kaj kiuj objektoj estas "
"haveblaj en tiu ŝablono."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "Iloj por via retumilo por rapide aliri la administran funkciaron."

msgid "Please install docutils"
msgstr "Bonvolu instali docutils-n"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Bonvolu demandi al viaj administrantoj instali <a href=\"%(link)s"
"\">docutils</a>-n."

#, python-format
msgid "Model: %(name)s"
msgstr "Modelo: %(name)s"

msgid "Fields"
msgstr "Kampoj"

msgid "Field"
msgstr "Kampo"

msgid "Type"
msgstr "Tipo"

msgid "Description"
msgstr "Priskribo"

msgid "Methods with arguments"
msgstr "Metodoj kun argumentoj"

msgid "Method"
msgstr "Metodo"

msgid "Arguments"
msgstr "Argumentoj"

msgid "Back to Model documentation"
msgstr "Reen al la modelo dokumentaro"

msgid "Model documentation"
msgstr "Modelo dokumentaro"

msgid "Model groups"
msgstr "Modelaj grupoj"

msgid "Templates"
msgstr "Ŝablonoj"

#, python-format
msgid "Template: %(name)s"
msgstr "Ŝablono: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Ŝablono: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr ""

msgid "(does not exist)"
msgstr "(ne ekzistas)"

msgid "Back to Documentation"
msgstr "Reen al dokumentaro"

msgid "Template filters"
msgstr "Ŝablonaj filtriloj"

msgid "Template filter documentation"
msgstr "Ŝablona filtrila dokumentaro"

msgid "Built-in filters"
msgstr "Integritaj filtriloj"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Por uzi tiujn filtrilojn, metu <code>%(code)s</code> en via ŝablono antaŭ "
"uzi la filtrilo."

msgid "Template tags"
msgstr "Ŝablonaj etikedoj"

msgid "Template tag documentation"
msgstr "Ŝablona etikeda dokumentado"

msgid "Built-in tags"
msgstr "Integritaj etikedoj"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Por uzi tiujn etikedojn, metu <code>%(code)s</code> en via ŝablono antaŭ uzi "
"la etikedon."

#, python-format
msgid "View: %(name)s"
msgstr "Vido: %(name)s"

msgid "Context:"
msgstr "Kunteksto:"

msgid "Templates:"
msgstr "Ŝablonoj:"

msgid "Back to View documentation"
msgstr "Reen al vidaj dokumentaro"

msgid "View documentation"
msgstr "Vida dokumentaro"

msgid "Jump to namespace"
msgstr "Iri al nomspaco"

msgid "Empty namespace"
msgstr "Malplena nomspaco"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Vidoj per nomspaco %(name)s"

msgid "Views by empty namespace"
msgstr "Vidoj per malplenaj nomspacoj"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Vida funkcio: <code>%(full_name)s</code>. Nomo: <code>%(url_name)s</code>.\n"

msgid "tag:"
msgstr "etikedo:"

msgid "filter:"
msgstr "filtro:"

msgid "view:"
msgstr "vido:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Aplikaĵo %(app_label)r ne trovita"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Modelo %(model_name)r netrovita en aplikaĵo %(app_label)r"

msgid "model:"
msgstr "modelo:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "la rilatita `%(app_label)s.%(data_type)s` objekto"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "rilatitaj `%(app_label)s.%(object_name)s` objektoj"

#, python-format
msgid "all %s"
msgstr "ĉiuj %s"

#, python-format
msgid "number of %s"
msgstr "nombro da %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s ne ŝajnas esti URLmotiva objekto"

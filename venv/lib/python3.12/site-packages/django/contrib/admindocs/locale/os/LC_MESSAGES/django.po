# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Ossetic (http://www.transifex.com/django/django/language/"
"os/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: os\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr ""

msgid "Home"
msgstr "Хӕдзар"

msgid "Documentation"
msgstr "Документаци"

msgid "Bookmarklets"
msgstr "Букмарклеттӕ"

msgid "Documentation bookmarklets"
msgstr "Документацийы букмарклеттӕ"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""

msgid "Documentation for this page"
msgstr "Ацы фарсы документаци"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "Цыфӕнды фарсӕй дӕ уыцы фарс цы хуыз фӕлдисы, уый документацимӕ ӕрвиты."

msgid "Tags"
msgstr "Тӕгтӕ"

msgid "List of all the template tags and their functions."
msgstr ""

msgid "Filters"
msgstr "Фӕрсудзӕнтӕ"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""

msgid "Models"
msgstr "Моделтӕ"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""

msgid "Views"
msgstr "Хуызтӕ"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""

msgid "Please install docutils"
msgstr ""

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""

#, python-format
msgid "Model: %(name)s"
msgstr ""

msgid "Fields"
msgstr ""

msgid "Field"
msgstr ""

msgid "Type"
msgstr ""

msgid "Description"
msgstr ""

msgid "Methods with arguments"
msgstr ""

msgid "Method"
msgstr ""

msgid "Arguments"
msgstr ""

msgid "Back to Model documentation"
msgstr ""

msgid "Model documentation"
msgstr ""

msgid "Model groups"
msgstr ""

msgid "Templates"
msgstr "Хуызӕгтӕ"

#, python-format
msgid "Template: %(name)s"
msgstr ""

#, python-format
msgid "Template: \"%(name)s\""
msgstr ""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr ""

msgid "(does not exist)"
msgstr ""

msgid "Back to Documentation"
msgstr ""

msgid "Template filters"
msgstr ""

msgid "Template filter documentation"
msgstr ""

msgid "Built-in filters"
msgstr ""

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""

msgid "Template tags"
msgstr ""

msgid "Template tag documentation"
msgstr ""

msgid "Built-in tags"
msgstr ""

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#, python-format
msgid "View: %(name)s"
msgstr ""

msgid "Context:"
msgstr ""

msgid "Templates:"
msgstr ""

msgid "Back to View documentation"
msgstr ""

msgid "View documentation"
msgstr ""

msgid "Jump to namespace"
msgstr ""

msgid "Empty namespace"
msgstr ""

#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

msgid "Views by empty namespace"
msgstr ""

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""

msgid "tag:"
msgstr "тӕг:"

msgid "filter:"
msgstr "фӕрсудзӕн:"

msgid "view:"
msgstr "хуыз:"

#, python-format
msgid "App %(app_label)r not found"
msgstr ""

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Модел %(model_name)r ӕфтуан %(app_label)r-ы мидӕг нӕ разынд"

msgid "model:"
msgstr "модел"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "хӕстӕг `%(app_label)s.%(data_type)s` объект"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "хӕстӕг `%(app_label)s.%(object_name)s` объекттӕ"

#, python-format
msgid "all %s"
msgstr "%s иууылдӕр"

#, python-format
msgid "number of %s"
msgstr "%s нымӕц"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s url-ы хуызӕгы объект нӕу"

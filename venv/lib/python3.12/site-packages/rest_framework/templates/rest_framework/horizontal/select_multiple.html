{% load i18n %}
{% load rest_framework %}

{% trans "No items to select." as no_items %}

<div class="form-group">
  {% if field.label %}
    <label class="col-sm-2 control-label {% if style.hide_label %}sr-only{% endif %}">
      {{ field.label }}
    </label>
  {% endif %}

  <div class="col-sm-10">
    <select multiple class="form-control" name="{{ field.name }}">
      {% for select in field.iter_options %}
        {% if select.start_option_group %}
          <optgroup label="{{ select.label }}">
        {% elif select.end_option_group %}
          </optgroup>
        {% else %}
          <option value="{{ select.value }}" {% if select.value|as_string in field.value|as_list_of_strings %}selected{% endif %} {% if select.disabled %}disabled{% endif %}>{{ select.display_text }}</option>
        {% endif %}
      {% empty %}
          <option>{{ no_items }}</option>
      {% endfor %}
    </select>

    {% if field.errors %}
      {% for error in field.errors %}
        <span class="help-block">{{ error }}</span>
      {% endfor %}
    {% endif %}

    {% if field.help_text %}
      <span class="help-block">{{ field.help_text|safe }}</span>
    {% endif %}
  </div>
</div>
